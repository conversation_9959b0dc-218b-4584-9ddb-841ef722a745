{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.10.1", "generated_at": "2025-06-19T06:15:58.598446Z", "invocation_id": "5d050013-71f1-4849-abf5-675a5fb8b528", "invocation_started_at": "2025-06-19T06:15:53.297608Z", "env": {}}, "results": [{"status": "success", "timing": [{"name": "compile", "started_at": "2025-06-19T06:15:56.744580Z", "completed_at": "2025-06-19T06:15:56.755585Z"}, {"name": "execute", "started_at": "2025-06-19T06:15:56.755585Z", "completed_at": "2025-06-19T06:15:58.598446Z"}], "thread_id": "MainThread", "execution_time": 1.853866, "adapter_response": {}, "message": null, "failures": 0, "unique_id": "macro.dbt.run_query", "compiled": false, "compiled_code": null, "relation_name": null, "batch_results": null}], "elapsed_time": 1.853866, "args": {"log_cache_events": false, "store_failures": false, "macro": "run_query", "require_explicit_package_overrides_for_builtin_materializations": true, "favor_state": false, "require_all_warnings_handled_by_warn_error": false, "quiet": false, "show_all_deprecations": false, "validate_macro_args": false, "vars": {}, "debug": false, "require_yaml_configuration_for_mf_time_spines": false, "log_file_max_bytes": 10485760, "use_fast_test_edges": false, "invocation_command": "dbt run-operation run_query --args {sql: 'SHOW DATABASES'}", "print": true, "require_resource_names_without_spaces": true, "send_anonymous_usage_stats": true, "static_parser": true, "introspect": true, "printer_width": 80, "log_level_file": "debug", "state_modified_compare_more_unrendered_values": false, "indirect_selection": "eager", "full_refresh": false, "skip_nodes_if_on_run_start_fails": false, "partial_parse": true, "log_format_file": "debug", "cache_selected_only": false, "show_resource_report": false, "version_check": true, "write_json": true, "use_colors_file": true, "upload_to_artifacts_ingest_api": false, "args": {"sql": "SHOW DATABASES"}, "partial_parse_file_diff": true, "strict_mode": false, "fail_fast": false, "macro_debugging": false, "defer": false, "log_level": "info", "require_batched_execution_for_custom_microbatch_strategy": false, "populate_cache": true, "which": "run-operation", "require_nested_cumulative_type_params": false, "state_modified_compare_vars": false, "source_freshness_run_project_hooks": true, "use_colors": true, "single_threaded": false, "log_format": "default", "use_experimental_parser": false}}