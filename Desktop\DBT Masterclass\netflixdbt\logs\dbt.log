[0m10:41:48.795222 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022076C89A50>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022077094C10>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022077095850>]}


============================== 10:41:48.833224 | ddb088d6-03d3-4fc9-9b72-aff4c5cff3fb ==============================
[0m10:41:48.833224 [info ] [MainThread]: Running with dbt=1.10.1
[0m10:41:48.835228 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'version_check': 'True', 'debug': 'False', 'log_path': 'logs', 'fail_fast': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'introspect': 'True', 'log_format': 'default', 'invocation_command': 'dbt init netflix', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m10:41:48.887226 [debug] [MainThread]: Starter project path: C:\Users\<USER>\Desktop\DBT Masterclass\netflixdbt\venv\Lib\site-packages\dbt\include\starter_project
[0m10:41:48.934225 [info ] [MainThread]: 
Your new dbt project "netflix" was created!

For more information on how to configure the profiles.yml file,
please consult the dbt documentation here:

  https://docs.getdbt.com/docs/configure-your-profile

One more thing:

Need help? Don't hesitate to reach out to us via GitHub issues or on Slack:

  https://community.getdbt.com/

Happy modeling!

[0m10:41:48.940226 [info ] [MainThread]: Setting up your profile.
[0m10:48:17.953192 [info ] [MainThread]: Profile netflix written to C:\Users\<USER>\.dbt\profiles.yml using target's profile_template.yml and your supplied values. Run 'dbt debug' to validate the connection.
[0m10:48:17.959200 [debug] [MainThread]: Command `dbt init` succeeded at 10:48:17.958203 after 389.34 seconds
[0m10:48:17.961197 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000220770B30D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022070690C90>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000022076377190>]}
[0m10:48:17.963202 [debug] [MainThread]: Flushing usage events
[0m10:48:19.436563 [debug] [MainThread]: An error was encountered while trying to flush usage events
