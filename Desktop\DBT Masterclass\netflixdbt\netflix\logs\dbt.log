[0m11:00:15.497322 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311BB1D210>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311BB878D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311BB5A650>]}


============================== 11:00:15.552645 | d1e36b53-13a8-4230-8362-68be5052022e ==============================
[0m11:00:15.552645 [info ] [MainThread]: Running with dbt=1.10.1
[0m11:00:15.555646 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'debug': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'log_path': 'C:\\Users\\<USER>\\Desktop\\DBT Masterclass\\netflixdbt\\netflix\\logs', 'fail_fast': 'False', 'version_check': 'True', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'log_format': 'default', 'invocation_command': 'dbt run', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'introspect': 'True', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m11:00:19.374344 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'd1e36b53-13a8-4230-8362-68be5052022e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311B4E0AD0>]}
[0m11:00:19.499343 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'd1e36b53-13a8-4230-8362-68be5052022e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311DB59810>]}
[0m11:00:19.502340 [info ] [MainThread]: Registered adapter: snowflake=1.9.0
[0m11:00:20.329572 [debug] [MainThread]: checksum: 2c026f85d539c10c69b093157526ce33d0bf5533489eafb07fe5172434ec7740, vars: {}, profile: , target: , version: 1.10.1
[0m11:00:20.332574 [info ] [MainThread]: Unable to do partial parsing because saved manifest not found. Starting full parse.
[0m11:00:20.333575 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'partial_parser', 'label': 'd1e36b53-13a8-4230-8362-68be5052022e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311DB871D0>]}
[0m11:00:23.050787 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 1 unused configuration paths:
- models.netflix.example
[0m11:00:23.061786 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': 'd1e36b53-13a8-4230-8362-68be5052022e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311A8605D0>]}
[0m11:00:23.222783 [debug] [MainThread]: Wrote artifact WritableManifest to C:\Users\<USER>\Desktop\DBT Masterclass\netflixdbt\netflix\target\manifest.json
[0m11:00:23.253795 [debug] [MainThread]: Wrote artifact SemanticManifest to C:\Users\<USER>\Desktop\DBT Masterclass\netflixdbt\netflix\target\semantic_manifest.json
[0m11:00:23.475614 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': 'd1e36b53-13a8-4230-8362-68be5052022e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311E207910>]}
[0m11:00:23.478614 [info ] [MainThread]: Found 1 model, 472 macros
[0m11:00:23.480612 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'd1e36b53-13a8-4230-8362-68be5052022e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311BB17A10>]}
[0m11:00:23.485611 [info ] [MainThread]: 
[0m11:00:23.490615 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m11:00:23.493617 [info ] [MainThread]: 
[0m11:00:23.500618 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m11:00:23.508620 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MOVIELENS'
[0m11:00:23.755607 [debug] [ThreadPool]: Using snowflake connection "list_MOVIELENS"
[0m11:00:23.756607 [debug] [ThreadPool]: On list_MOVIELENS: /* {"app": "dbt", "dbt_version": "1.10.1", "profile_name": "netflix", "target_name": "dev", "connection_name": "list_MOVIELENS"} */
show terse schemas in database MOVIELENS
    limit 10000
[0m11:00:23.758612 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m11:00:27.912193 [debug] [ThreadPool]: Snowflake adapter: Snowflake query id: 01bd20a8-0001-1acf-0009-b3be00019072
[0m11:00:27.914174 [debug] [ThreadPool]: Snowflake adapter: Snowflake error: 002043 (02000): SQL compilation error:
Object does not exist, or operation cannot be performed.
[0m11:00:27.917165 [debug] [ThreadPool]: Snowflake adapter: Error running SQL: macro list_schemas
[0m11:00:27.919167 [debug] [ThreadPool]: Snowflake adapter: Rolling back transaction.
[0m11:00:27.923165 [debug] [MainThread]: Connection 'master' was properly closed.
[0m11:00:27.925174 [debug] [MainThread]: Connection 'list_MOVIELENS' was left open.
[0m11:00:27.927174 [debug] [MainThread]: On list_MOVIELENS: Close
[0m11:00:28.451589 [info ] [MainThread]: 
[0m11:00:28.453591 [info ] [MainThread]: Finished running  in 0 hours 0 minutes and 4.95 seconds (4.95s).
[0m11:00:28.455593 [error] [MainThread]: Encountered an error:
Runtime Error
  Database error while listing schemas in database "MOVIELENS"
  Database Error
    002043 (02000): SQL compilation error:
    Object does not exist, or operation cannot be performed.
[0m11:00:28.460595 [debug] [MainThread]: Command `dbt run` failed at 11:00:28.460595 after 13.11 seconds
[0m11:00:28.462600 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311BB878D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311BB85D10>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002311DFBD310>]}
[0m11:00:28.464597 [debug] [MainThread]: Flushing usage events
[0m11:00:30.749906 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m11:03:00.667812 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002662677E910>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002662677E3D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000002662677ED90>]}


============================== 11:03:00.674814 | 6b6adfd7-491f-4e03-ae36-855c3ad3d67e ==============================
[0m11:03:00.674814 [info ] [MainThread]: Running with dbt=1.10.1
[0m11:03:00.676812 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'version_check': 'True', 'debug': 'False', 'log_path': 'C:\\Users\\<USER>\\Desktop\\DBT Masterclass\\netflixdbt\\netflix\\logs', 'fail_fast': 'False', 'warn_error': 'None', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'False', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'introspect': 'True', 'log_format': 'default', 'invocation_command': 'dbt run', 'target_path': 'None', 'static_parser': 'True', 'send_anonymous_usage_stats': 'True'}
[0m11:03:01.762224 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': '6b6adfd7-491f-4e03-ae36-855c3ad3d67e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000026626487C50>]}
[0m11:03:01.884217 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': '6b6adfd7-491f-4e03-ae36-855c3ad3d67e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000266287B9790>]}
[0m11:03:01.887220 [info ] [MainThread]: Registered adapter: snowflake=1.9.0
[0m11:03:02.681418 [debug] [MainThread]: checksum: 2c026f85d539c10c69b093157526ce33d0bf5533489eafb07fe5172434ec7740, vars: {}, profile: , target: , version: 1.10.1
[0m11:03:03.013418 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 1 files changed.
[0m11:03:03.015418 [debug] [MainThread]: Partial parsing: updated file: netflix://models\staging\src_movies.sql
[0m11:03:03.484648 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 1 unused configuration paths:
- models.netflix.example
[0m11:03:03.495643 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': '6b6adfd7-491f-4e03-ae36-855c3ad3d67e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000026628C32D10>]}
[0m11:03:03.641647 [debug] [MainThread]: Wrote artifact WritableManifest to C:\Users\<USER>\Desktop\DBT Masterclass\netflixdbt\netflix\target\manifest.json
[0m11:03:03.672643 [debug] [MainThread]: Wrote artifact SemanticManifest to C:\Users\<USER>\Desktop\DBT Masterclass\netflixdbt\netflix\target\semantic_manifest.json
[0m11:03:03.713646 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': '6b6adfd7-491f-4e03-ae36-855c3ad3d67e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000026628E2B9D0>]}
[0m11:03:03.715647 [info ] [MainThread]: Found 1 model, 472 macros
[0m11:03:03.716643 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': '6b6adfd7-491f-4e03-ae36-855c3ad3d67e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000026628920990>]}
[0m11:03:03.720640 [info ] [MainThread]: 
[0m11:03:03.721644 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m11:03:03.722646 [info ] [MainThread]: 
[0m11:03:03.724646 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m11:03:03.727646 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MOVIELENS'
[0m11:03:04.061647 [debug] [ThreadPool]: Using snowflake connection "list_MOVIELENS"
[0m11:03:04.063649 [debug] [ThreadPool]: On list_MOVIELENS: /* {"app": "dbt", "dbt_version": "1.10.1", "profile_name": "netflix", "target_name": "dev", "connection_name": "list_MOVIELENS"} */
show terse schemas in database MOVIELENS
    limit 10000
[0m11:03:04.064647 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m11:03:05.512946 [debug] [ThreadPool]: Snowflake adapter: Snowflake query id: 01bd20ab-0001-19ea-0009-b3be0001c05e
[0m11:03:05.515946 [debug] [ThreadPool]: Snowflake adapter: Snowflake error: 002043 (02000): SQL compilation error:
Object does not exist, or operation cannot be performed.
[0m11:03:05.518912 [debug] [ThreadPool]: Snowflake adapter: Error running SQL: macro list_schemas
[0m11:03:05.519915 [debug] [ThreadPool]: Snowflake adapter: Rolling back transaction.
[0m11:03:05.521913 [debug] [MainThread]: Connection 'master' was properly closed.
[0m11:03:05.522919 [debug] [MainThread]: Connection 'list_MOVIELENS' was left open.
[0m11:03:05.523912 [debug] [MainThread]: On list_MOVIELENS: Close
[0m11:03:05.904318 [info ] [MainThread]: 
[0m11:03:05.909508 [info ] [MainThread]: Finished running  in 0 hours 0 minutes and 2.18 seconds (2.18s).
[0m11:03:05.916483 [error] [MainThread]: Encountered an error:
Runtime Error
  Database error while listing schemas in database "MOVIELENS"
  Database Error
    002043 (02000): SQL compilation error:
    Object does not exist, or operation cannot be performed.
[0m11:03:05.929484 [debug] [MainThread]: Command `dbt run` failed at 11:03:05.927486 after 5.41 seconds
[0m11:03:05.935500 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000266267ED510>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000266267EF350>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x00000266200A2C50>]}
[0m11:03:05.939471 [debug] [MainThread]: Flushing usage events
[0m11:03:07.400917 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m11:05:38.911304 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F4DFDEDD0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F4E016B50>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F4DFDE050>]}


============================== 11:05:38.919305 | c25baf37-ec52-4da7-9f33-c5498699406e ==============================
[0m11:05:38.919305 [info ] [MainThread]: Running with dbt=1.10.1
[0m11:05:38.921307 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'version_check': 'True', 'warn_error': 'None', 'log_path': 'C:\\Users\\<USER>\\Desktop\\DBT Masterclass\\netflixdbt\\netflix\\logs', 'debug': 'False', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'False', 'quiet': 'False', 'no_print': 'None', 'log_format': 'default', 'static_parser': 'True', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'introspect': 'True', 'target_path': 'None', 'invocation_command': 'dbt run', 'send_anonymous_usage_stats': 'True'}
[0m11:05:40.036493 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'c25baf37-ec52-4da7-9f33-c5498699406e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F5011AB50>]}
[0m11:05:40.218493 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'adapter_info', 'label': 'c25baf37-ec52-4da7-9f33-c5498699406e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F50019950>]}
[0m11:05:40.223496 [info ] [MainThread]: Registered adapter: snowflake=1.9.0
[0m11:05:41.193494 [debug] [MainThread]: checksum: 2c026f85d539c10c69b093157526ce33d0bf5533489eafb07fe5172434ec7740, vars: {}, profile: , target: , version: 1.10.1
[0m11:05:41.521802 [debug] [MainThread]: Partial parsing enabled: 0 files deleted, 0 files added, 0 files changed.
[0m11:05:41.522801 [debug] [MainThread]: Partial parsing enabled, no changes found, skipping parsing
[0m11:05:41.533803 [warn ] [MainThread]: [[33mWARNING[0m]: Configuration paths exist in your dbt_project.yml file which do not apply to any resources.
There are 1 unused configuration paths:
- models.netflix.example
[0m11:05:41.595801 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'load_project', 'label': 'c25baf37-ec52-4da7-9f33-c5498699406e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F5045E810>]}
[0m11:05:41.719803 [debug] [MainThread]: Wrote artifact WritableManifest to C:\Users\<USER>\Desktop\DBT Masterclass\netflixdbt\netflix\target\manifest.json
[0m11:05:41.746481 [debug] [MainThread]: Wrote artifact SemanticManifest to C:\Users\<USER>\Desktop\DBT Masterclass\netflixdbt\netflix\target\semantic_manifest.json
[0m11:05:41.788484 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'resource_counts', 'label': 'c25baf37-ec52-4da7-9f33-c5498699406e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F5057DA90>]}
[0m11:05:41.790484 [info ] [MainThread]: Found 1 model, 472 macros
[0m11:05:41.793485 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'runnable_timing', 'label': 'c25baf37-ec52-4da7-9f33-c5498699406e', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F503AF750>]}
[0m11:05:41.796487 [info ] [MainThread]: 
[0m11:05:41.799484 [info ] [MainThread]: Concurrency: 1 threads (target='dev')
[0m11:05:41.801482 [info ] [MainThread]: 
[0m11:05:41.803487 [debug] [MainThread]: Acquiring new snowflake connection 'master'
[0m11:05:41.807486 [debug] [ThreadPool]: Acquiring new snowflake connection 'list_MOVIELENS'
[0m11:05:42.060480 [debug] [ThreadPool]: Using snowflake connection "list_MOVIELENS"
[0m11:05:42.061481 [debug] [ThreadPool]: On list_MOVIELENS: /* {"app": "dbt", "dbt_version": "1.10.1", "profile_name": "netflix", "target_name": "dev", "connection_name": "list_MOVIELENS"} */
show terse schemas in database MOVIELENS
    limit 10000
[0m11:05:42.062480 [debug] [ThreadPool]: Opening a new connection, currently in state init
[0m11:05:43.562865 [debug] [ThreadPool]: Snowflake adapter: Snowflake query id: 01bd20ad-0001-1ad0-0009-b3be0001e05a
[0m11:05:43.565837 [debug] [ThreadPool]: Snowflake adapter: Snowflake error: 002043 (02000): SQL compilation error:
Object does not exist, or operation cannot be performed.
[0m11:05:43.569834 [debug] [ThreadPool]: Snowflake adapter: Error running SQL: macro list_schemas
[0m11:05:43.572865 [debug] [ThreadPool]: Snowflake adapter: Rolling back transaction.
[0m11:05:43.576828 [debug] [MainThread]: Connection 'master' was properly closed.
[0m11:05:43.578835 [debug] [MainThread]: Connection 'list_MOVIELENS' was left open.
[0m11:05:43.579829 [debug] [MainThread]: On list_MOVIELENS: Close
[0m11:05:44.514180 [info ] [MainThread]: 
[0m11:05:44.520182 [info ] [MainThread]: Finished running  in 0 hours 0 minutes and 2.71 seconds (2.71s).
[0m11:05:44.526162 [error] [MainThread]: Encountered an error:
Runtime Error
  Database error while listing schemas in database "MOVIELENS"
  Database Error
    002043 (02000): SQL compilation error:
    Object does not exist, or operation cannot be performed.
[0m11:05:44.540161 [debug] [MainThread]: Command `dbt run` failed at 11:05:44.539160 after 5.77 seconds
[0m11:05:44.543154 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F4DFDEDD0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F4DFDCD90>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x0000016F4D316D50>]}
[0m11:05:44.546154 [debug] [MainThread]: Flushing usage events
[0m11:05:48.163154 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m11:06:06.519748 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001CF0E23E750>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001CF0DD9CE50>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001CF0E2ABC10>]}


============================== 11:06:06.527747 | 3da35efe-fecb-468b-8184-dcfe9bddb0dc ==============================
[0m11:06:06.527747 [info ] [MainThread]: Running with dbt=1.10.1
[0m11:06:06.529750 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'write_json': 'True', 'log_cache_events': 'False', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'version_check': 'True', 'debug': 'False', 'log_path': 'C:\\Users\\<USER>\\Desktop\\DBT Masterclass\\netflixdbt\\netflix\\logs', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'fail_fast': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'empty': 'None', 'quiet': 'False', 'no_print': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'static_parser': 'True', 'invocation_command': 'dbt debug', 'introspect': 'True', 'target_path': 'None', 'log_format': 'default', 'send_anonymous_usage_stats': 'True'}
[0m11:06:06.569750 [info ] [MainThread]: dbt version: 1.10.1
[0m11:06:06.571750 [info ] [MainThread]: python version: 3.11.9
[0m11:06:06.573749 [info ] [MainThread]: python path: C:\Users\<USER>\Desktop\DBT Masterclass\netflixdbt\venv\Scripts\python.exe
[0m11:06:06.575748 [info ] [MainThread]: os info: Windows-10-10.0.19045-SP0
[0m11:06:07.354024 [info ] [MainThread]: Using profiles dir at C:\Users\<USER>\.dbt
[0m11:06:07.356021 [info ] [MainThread]: Using profiles.yml file at C:\Users\<USER>\.dbt\profiles.yml
[0m11:06:07.358022 [info ] [MainThread]: Using dbt_project.yml file at C:\Users\<USER>\Desktop\DBT Masterclass\netflixdbt\netflix\dbt_project.yml
[0m11:06:07.360020 [info ] [MainThread]: adapter type: snowflake
[0m11:06:07.362027 [info ] [MainThread]: adapter version: 1.9.0
[0m11:06:07.612018 [info ] [MainThread]: Configuration:
[0m11:06:07.613026 [info ] [MainThread]:   profiles.yml file [[32mOK found and valid[0m]
[0m11:06:07.615020 [info ] [MainThread]:   dbt_project.yml file [[32mOK found and valid[0m]
[0m11:06:07.616021 [info ] [MainThread]: Required dependencies:
[0m11:06:07.618022 [debug] [MainThread]: Executing "git --help"
[0m11:06:07.763023 [debug] [MainThread]: STDOUT: "b"usage: git [-v | --version] [-h | --help] [-C <path>] [-c <name>=<value>]\n           [--exec-path[=<path>]] [--html-path] [--man-path] [--info-path]\n           [-p | --paginate | -P | --no-pager] [--no-replace-objects] [--no-lazy-fetch]\n           [--no-optional-locks] [--no-advice] [--bare] [--git-dir=<path>]\n           [--work-tree=<path>] [--namespace=<name>] [--config-env=<name>=<envvar>]\n           <command> [<args>]\n\nThese are common Git commands used in various situations:\n\nstart a working area (see also: git help tutorial)\n   clone      Clone a repository into a new directory\n   init       Create an empty Git repository or reinitialize an existing one\n\nwork on the current change (see also: git help everyday)\n   add        Add file contents to the index\n   mv         Move or rename a file, a directory, or a symlink\n   restore    Restore working tree files\n   rm         Remove files from the working tree and from the index\n\nexamine the history and state (see also: git help revisions)\n   bisect     Use binary search to find the commit that introduced a bug\n   diff       Show changes between commits, commit and working tree, etc\n   grep       Print lines matching a pattern\n   log        Show commit logs\n   show       Show various types of objects\n   status     Show the working tree status\n\ngrow, mark and tweak your common history\n   backfill   Download missing objects in a partial clone\n   branch     List, create, or delete branches\n   commit     Record changes to the repository\n   merge      Join two or more development histories together\n   rebase     Reapply commits on top of another base tip\n   reset      Reset current HEAD to the specified state\n   switch     Switch branches\n   tag        Create, list, delete or verify a tag object signed with GPG\n\ncollaborate (see also: git help workflows)\n   fetch      Download objects and refs from another repository\n   pull       Fetch from and integrate with another repository or a local branch\n   push       Update remote refs along with associated objects\n\n'git help -a' and 'git help -g' list available subcommands and some\nconcept guides. See 'git help <command>' or 'git help <concept>'\nto read about a specific subcommand or concept.\nSee 'git help git' for an overview of the system.\n""
[0m11:06:07.765023 [debug] [MainThread]: STDERR: "b''"
[0m11:06:07.767026 [info ] [MainThread]:  - git [[32mOK found[0m]

[0m11:06:07.770024 [info ] [MainThread]: Connection:
[0m11:06:07.772025 [info ] [MainThread]:   account: RIAJEDC-MP54186
[0m11:06:07.774024 [info ] [MainThread]:   user: dbt
[0m11:06:07.776025 [info ] [MainThread]:   database: MOVIELENS
[0m11:06:07.780026 [info ] [MainThread]:   warehouse: COMPUTE_WH
[0m11:06:07.782022 [info ] [MainThread]:   role: TRANSFORM
[0m11:06:07.784026 [info ] [MainThread]:   schema: RAW
[0m11:06:07.787027 [info ] [MainThread]:   authenticator: None
[0m11:06:07.790030 [info ] [MainThread]:   oauth_client_id: None
[0m11:06:07.792024 [info ] [MainThread]:   query_tag: None
[0m11:06:07.799037 [info ] [MainThread]:   client_session_keep_alive: False
[0m11:06:07.801022 [info ] [MainThread]:   host: None
[0m11:06:07.804027 [info ] [MainThread]:   port: None
[0m11:06:07.807024 [info ] [MainThread]:   proxy_host: None
[0m11:06:07.810027 [info ] [MainThread]:   proxy_port: None
[0m11:06:07.814024 [info ] [MainThread]:   protocol: None
[0m11:06:07.817028 [info ] [MainThread]:   connect_retries: 1
[0m11:06:07.820028 [info ] [MainThread]:   connect_timeout: None
[0m11:06:07.822024 [info ] [MainThread]:   retry_on_database_errors: False
[0m11:06:07.825024 [info ] [MainThread]:   retry_all: False
[0m11:06:07.827022 [info ] [MainThread]:   insecure_mode: False
[0m11:06:07.833026 [info ] [MainThread]:   reuse_connections: True
[0m11:06:07.837023 [info ] [MainThread]: Registered adapter: snowflake=1.9.0
[0m11:06:08.794343 [debug] [MainThread]: Acquiring new snowflake connection 'debug'
[0m11:06:09.017341 [debug] [MainThread]: Using snowflake connection "debug"
[0m11:06:09.018341 [debug] [MainThread]: On debug: select 1 as id
[0m11:06:09.019346 [debug] [MainThread]: Opening a new connection, currently in state init
[0m11:06:10.216519 [debug] [MainThread]: SQL status: SUCCESS 1 in 1.198 seconds
[0m11:06:10.220516 [info ] [MainThread]:   Connection test: [[32mOK connection ok[0m]

[0m11:06:10.222517 [info ] [MainThread]: [32mAll checks passed![0m
[0m11:06:10.226514 [debug] [MainThread]: Command `dbt debug` succeeded at 11:06:10.226514 after 3.85 seconds
[0m11:06:10.228516 [debug] [MainThread]: Connection 'debug' was left open.
[0m11:06:10.230519 [debug] [MainThread]: On debug: Close
[0m11:06:10.555512 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001CF07ADDD90>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001CF107CEF90>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001CF10801CD0>]}
[0m11:06:10.556515 [debug] [MainThread]: Flushing usage events
[0m11:06:11.831708 [debug] [MainThread]: An error was encountered while trying to flush usage events
[0m11:09:25.984852 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'start', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D67CA8A0D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D67BD2DF90>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D67CACD410>]}


============================== 11:09:25.991858 | af08748d-8f19-46e9-b8bd-7a7628993357 ==============================
[0m11:09:25.991858 [info ] [MainThread]: Running with dbt=1.10.1
[0m11:09:25.994855 [debug] [MainThread]: running dbt with arguments {'printer_width': '80', 'indirect_selection': 'eager', 'log_cache_events': 'False', 'write_json': 'True', 'partial_parse': 'True', 'cache_selected_only': 'False', 'warn_error': 'None', 'fail_fast': 'False', 'profiles_dir': 'C:\\Users\\<USER>\\.dbt', 'log_path': 'c:\\Users\\<USER>\\Desktop\\DBT Masterclass\\netflixdbt\\netflix\\logs', 'version_check': 'True', 'debug': 'False', 'use_colors': 'True', 'use_experimental_parser': 'False', 'no_print': 'None', 'quiet': 'False', 'empty': 'None', 'warn_error_options': 'WarnErrorOptionsV2(error=[], warn=[], silence=[])', 'introspect': 'True', 'log_format': 'default', 'static_parser': 'True', 'target_path': 'None', 'invocation_command': 'dbt ', 'send_anonymous_usage_stats': 'True'}
[0m11:09:26.313858 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'project_id', 'label': 'af08748d-8f19-46e9-b8bd-7a7628993357', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D67CBED690>]}
[0m11:09:26.357853 [info ] [MainThread]: Warning: No packages were found in packages.yml
[0m11:09:26.361862 [info ] [MainThread]: Warning: No packages were found in packages.yml
[0m11:09:26.364854 [debug] [MainThread]: Command `cli deps` succeeded at 11:09:26.364854 after 0.54 seconds
[0m11:09:26.366853 [debug] [MainThread]: Sending event: {'category': 'dbt', 'action': 'invocation', 'label': 'end', 'context': [<snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D67CA8BBD0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D67CA895D0>, <snowplow_tracker.self_describing_json.SelfDescribingJson object at 0x000001D67CB2CA50>]}
[0m11:09:26.367853 [debug] [MainThread]: Flushing usage events
[0m11:09:27.573512 [debug] [MainThread]: An error was encountered while trying to flush usage events
