fixed:
  type: snowflake
prompts:
  account:
    hint: 'https://<this_value>.snowflakecomputing.com'
  user:
    hint: 'dev username'
  _choose_authentication_type:
    password:
      password:
        hint: 'dev password'
        hide_input: true
    keypair:
      private_key_path:
        hint: 'path/to/private.key'
      private_key_passphrase:
        hint: 'passphrase for the private key, if key is encrypted'
        hide_input: true
    sso:
      authenticator:
        hint: "'externalbrowser' or a valid Okta URL"
        default: 'externalbrowser'
  role:
    hint: 'dev role'
  warehouse:
    hint: 'warehouse name'
  database:
    hint: 'default database that dbt will build objects in'
  schema:
    hint: 'default schema that dbt will build objects in'
  threads:
    hint: '1 or more'
    type: 'int'
    default: 1
