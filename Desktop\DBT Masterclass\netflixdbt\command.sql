
virtualenv venv
. venv/Scripts/activate

-- install dbt snowflake
pip install dbt-snowflake==1.9.0

-- create dbt profile

mkdir %userprofile%\.dbt

-- initiate dbt project 
dbt init netflix

-- Add staging tables -> all will be views
-- Update dbt_project.yaml file to support of tables
dim:
  +materialized: table
--   +schema: dim
-- fct:
--   +materialized: incremental
--   +schema: fct
-- mart:
--   +materialized: table
--   +schema: mart
-- snapshots:
--   +schema: snapshots
MOVIELENS.RAW.RAW_GENOME_SCORES